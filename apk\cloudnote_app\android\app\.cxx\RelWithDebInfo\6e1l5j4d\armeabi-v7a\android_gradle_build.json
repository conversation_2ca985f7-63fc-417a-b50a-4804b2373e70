{"buildFiles": ["D:\\Tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\android\\app\\.cxx\\RelWithDebInfo\\6e1l5j4d\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\android\\app\\.cxx\\RelWithDebInfo\\6e1l5j4d\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Tools\\android_sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Tools\\android_sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}