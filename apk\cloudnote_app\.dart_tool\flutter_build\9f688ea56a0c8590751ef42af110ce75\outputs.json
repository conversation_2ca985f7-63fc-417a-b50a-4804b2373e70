["D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/quill_native_bridge_linux/assets/xclip", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.worklet.js", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/record_web/assets/js/record.fixwebmduration.js", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]