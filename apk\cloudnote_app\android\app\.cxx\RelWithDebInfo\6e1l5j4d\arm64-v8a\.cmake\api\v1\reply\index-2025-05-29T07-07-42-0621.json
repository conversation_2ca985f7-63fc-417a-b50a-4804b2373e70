{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Tools/android_sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Tools/android_sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Tools/android_sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Tools/android_sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-5027b2f8980469380065.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-6aa21a65b450deefc3f4.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-b7210d7b6ff22300a7a2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-6aa21a65b450deefc3f4.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-b7210d7b6ff22300a7a2.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5027b2f8980469380065.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}