import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../providers/markdown_provider.dart';
import '../providers/ai_provider.dart';
import '../widgets/markdown_editor_widget.dart';
import '../widgets/sync_button.dart';
import '../widgets/markdown_file_list_widget.dart';
import '../widgets/modern_components.dart';
import '../theme/app_theme.dart';
import '../services/media_processing_service.dart';
import '../services/filehost_upload_service.dart';
import '../services/auth_service.dart';

/// Notes主视图 - Markdown编辑器功能
class NotesView extends StatefulWidget {
  const NotesView({super.key});

  @override
  State<NotesView> createState() => _NotesViewState();

  // 静态方法用于从外部触发快速导入功能
  static void showQuickImportDialog(BuildContext context) {
    debugPrint('NotesView: showQuickImportDialog called');
    showDialog(
      context: context,
      builder: (context) => _QuickImportDialog(),
    );
  }

  static void showQuickImportDialogWithCamera(BuildContext context) {
    debugPrint('NotesView: showQuickImportDialogWithCamera called');
    showDialog(
      context: context,
      builder: (context) => _QuickImportDialog(autoTriggerCamera: true),
    );
  }


}

class _NotesViewState extends State<NotesView> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _showFileList = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  bool _wasSyncing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 初始化Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MarkdownProvider>().init();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Consumer<MarkdownProvider>(
      builder: (context, provider, child) {
        // 检查同步状态变化
        if (_wasSyncing && !provider.isSyncing) {
          // 同步刚刚完成
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _showSyncCompletedMessage();
          });
        }
        _wasSyncing = provider.isSyncing;

        return Scaffold(
          backgroundColor: colorScheme.surface,
          body: Column(
            children: [
              // Header with tabs and controls
              _buildHeader(theme),

              // Main content area
              Expanded(
                child: Row(
                  children: [
                    // File list sidebar
                    if (_showFileList) ...[
                      SizedBox(
                        width: 300,
                        child: _buildFileListSidebar(theme),
                      ),
                      VerticalDivider(
                        width: 1,
                        thickness: 1,
                        color: colorScheme.outline.withValues(alpha: 0.2),
                      ),
                    ],

                    // Editor area
                    Expanded(
                      child: _buildEditorArea(theme),
                    ),
                  ],
                ),
              ),
            ],
          ),
          floatingActionButton: _buildFloatingActionButton(colorScheme),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 12, 20, 16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.15),
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 🎛️ 现代化切换按钮
          Container(
            decoration: BoxDecoration(
              color: _showFileList
                ? colorScheme.primaryContainer.withValues(alpha: 0.8)
                : colorScheme.surfaceContainerHighest.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _showFileList = !_showFileList;
                });
              },
              icon: Icon(
                _showFileList ? Icons.menu_open_rounded : Icons.menu_rounded,
                color: _showFileList
                  ? colorScheme.primary
                  : colorScheme.onSurfaceVariant,
                size: 20,
              ),
              tooltip: _showFileList ? 'Hide Files' : 'Show Files',
            ),
          ),

          const SizedBox(width: 16),

          // 📝 现代化标题区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Markdown Editor',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                    letterSpacing: -0.5,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Write and organize your thoughts',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // 🔄 同步按钮
          const SyncButton(),
        ],
      ),
    );
  }

  Widget _buildFileListSidebar(ThemeData theme) {

    return Column(
      children: [
        // 侧边栏工具栏
        _buildSidebarToolbar(theme),

        // 搜索栏
        _buildSidebarSearchBar(theme),

        // 文件列表
        Expanded(
          child: Consumer<MarkdownProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (provider.error != null) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: theme.colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        provider.error!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => provider.loadFiles(),
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                );
              }

              final files = _searchQuery.isEmpty
                  ? provider.files
                  : provider.searchFiles(_searchQuery);

              return MarkdownFileListWidget(
                files: files,
                activeFile: provider.activeFile,
                onFileSelected: (file) {
                  provider.openFile(file);
                  // 选择文件后自动关闭侧边栏
                  setState(() {
                    _showFileList = false;
                  });
                },
                onFileRenamed: (file, newName) => provider.renameFile(file.id, newName),
                onFileDeleted: (file) => provider.deleteFile(file.id),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEditorArea(ThemeData theme) {
    return Consumer<MarkdownProvider>(
      builder: (context, provider, child) {
        if (provider.activeFile == null) {
          return _buildEmptyState(theme);
        }

        return MarkdownEditorWidget(
          key: ValueKey('${provider.activeFile!.id}_${provider.activeFile!.versionNumber}_${provider.activeFile!.updatedAt}'),
          file: provider.activeFile!,
          onContentChanged: (content) {
            provider.updateFileContent(provider.activeFile!.id, content);
          },
        );
      },
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return ModernEmptyState(
      icon: Icons.article_outlined,
      title: 'No file selected',
      subtitle: 'Choose a file from the sidebar or create a new markdown document to start writing',
      actionText: 'Create New File',
      onAction: _showCreateFileDialog,
    );
  }

  Widget _buildFloatingActionButton(ColorScheme colorScheme) {
    return FloatingActionButton(
      onPressed: _showCreateFileDialog,
      backgroundColor: colorScheme.primary,
      foregroundColor: colorScheme.onPrimary,
      elevation: 4,
      tooltip: 'Create New File',
      child: const Icon(
        Icons.add_rounded,
        size: 28,
      ),
    );
  }

  void _handleMenuAction(String action) {
    final provider = context.read<MarkdownProvider>();

    switch (action) {
      case 'sort_name':
        provider.sortFilesByName();
        break;
      case 'sort_modified':
        provider.sortFilesByModified();
        break;
      case 'quick_import':
        _showQuickImportDialog();
        break;
      case 'new_file':
        _showCreateFileDialog();
        break;
    }
  }

  void _showCreateFileDialog() {
    showDialog(
      context: context,
      builder: (context) => _CreateFileDialog(),
    );
  }

  void _showQuickImportDialog() {
    showDialog(
      context: context,
      builder: (context) => _QuickImportDialog(),
    );
  }

  void _showSyncCompletedMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('同步完成，文件已更新'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: '知道了',
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  Widget _buildSidebarToolbar(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 文件管理标题
          Expanded(
            child: Text(
              '文件管理',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // 同步按钮
          Consumer<MarkdownProvider>(
            builder: (context, provider, child) {
              return IconButton(
                onPressed: provider.isSyncing ? null : () {
                  provider.syncAllFiles();
                },
                icon: provider.isSyncing
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: colorScheme.primary,
                        ),
                      )
                    : const Icon(Icons.sync),
                tooltip: '同步文件',
                iconSize: 20,
              );
            },
          ),

          // 菜单按钮
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'sort_name',
                child: Row(
                  children: [
                    Icon(Icons.sort_by_alpha),
                    SizedBox(width: 8),
                    Text('按名称排序'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sort_modified',
                child: Row(
                  children: [
                    Icon(Icons.access_time),
                    SizedBox(width: 8),
                    Text('按修改时间排序'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              const PopupMenuItem(
                value: 'quick_import',
                child: Row(
                  children: [
                    Icon(Icons.content_paste),
                    SizedBox(width: 8),
                    Text('快速导入'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'new_file',
                child: Row(
                  children: [
                    Icon(Icons.add),
                    SizedBox(width: 8),
                    Text('新建文件'),
                  ],
                ),
              ),
            ],
            icon: const Icon(Icons.more_vert),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildSidebarSearchBar(ThemeData theme) {
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(8),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索文件...',
          prefixIcon: const Icon(Icons.search, size: 18),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: const Icon(Icons.clear, size: 18),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: colorScheme.surfaceContainerHighest,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          isDense: true,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }
}

class _CreateFileDialog extends StatefulWidget {
  @override
  State<_CreateFileDialog> createState() => _CreateFileDialogState();
}

class _CreateFileDialogState extends State<_CreateFileDialog> {
  final _controller = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('创建新文件'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _controller,
              decoration: const InputDecoration(
                labelText: '文件路径和名称',
                hintText: '例如: folder/filename 或 filename',
                helperText: '支持子目录，使用 / 分隔路径\n不需要.md后缀',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return '请输入文件路径和名称';
                }

                // 检查路径是否包含非法字符
                final path = value.trim();
                if (path.contains('..') || path.contains('\\') ||
                    path.contains('*') || path.contains('?') ||
                    path.contains('"') || path.contains('<') ||
                    path.contains('>') || path.contains('|')) {
                  return '路径包含非法字符';
                }

                return null;
              },
              autofocus: true,
              onFieldSubmitted: (_) => _createFile(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _createFile,
          child: const Text('创建'),
        ),
      ],
    );
  }

  void _createFile() {
    if (_formKey.currentState!.validate()) {
      final path = _controller.text.trim();
      // 确保路径以.md结尾
      final fullPath = path.endsWith('.md') ? path : '$path.md';
      context.read<MarkdownProvider>().createFile(filename: fullPath);
      Navigator.of(context).pop();
    }
  }
}

class _QuickImportDialog extends StatefulWidget {
  final bool autoTriggerCamera;

  const _QuickImportDialog({
    this.autoTriggerCamera = false,
  });

  @override
  State<_QuickImportDialog> createState() => _QuickImportDialogState();
}

class _QuickImportDialogState extends State<_QuickImportDialog> with TickerProviderStateMixin {
  final _controller = TextEditingController();
  final _focusNode = FocusNode();
  final AudioRecorder _audioRecorder = AudioRecorder();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isLoading = false;
  bool _isRecording = false;
  bool _isProcessing = false;
  bool _isUploading = false;
  String? _audioPath;
  String? _imagePath;
  String? _originalText;
  String? _uploadedFileUrl;
  String? _aiRecognitionResult;

  // 动画控制器
  late AnimationController _pulseAnimationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化脉冲动画控制器
    _pulseAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseAnimationController,
      curve: Curves.easeInOut,
    ));

    _loadClipboardContent();

    // 处理自动触发功能
    WidgetsBinding.instance.addPostFrameCallback((_) {
      debugPrint('_QuickImportDialog: PostFrameCallback - autoTriggerCamera: ${widget.autoTriggerCamera}');
      if (widget.autoTriggerCamera) {
        debugPrint('_QuickImportDialog: Auto triggering camera');
        _pickImage(ImageSource.camera);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _audioRecorder.dispose();
    _pulseAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadClipboardContent() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null && clipboardData!.text!.isNotEmpty) {
        setState(() {
          _controller.text = clipboardData.text!;
        });
      }
    } catch (e) {
      debugPrint('Failed to load clipboard content: $e');
    }
  }

  Future<void> _requestPermissions() async {
    await [
      Permission.microphone,
      Permission.storage,
      Permission.camera,
    ].request();
  }

  Future<void> _startRecording() async {
    try {
      await _requestPermissions();

      if (await _audioRecorder.hasPermission()) {
        final directory = await getExternalStorageDirectory();
        final audioDir = Directory('${directory!.path}/CloudNote/Audio');
        if (!await audioDir.exists()) {
          await audioDir.create(recursive: true);
        }

        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final path = '${audioDir.path}/audio_$timestamp.m4a';

        await _audioRecorder.start(const RecordConfig(), path: path);
        setState(() {
          _isRecording = true;
        });

        // 开始脉冲动画
        _pulseAnimationController.repeat(reverse: true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('录音启动失败: $e')),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      final path = await _audioRecorder.stop();
      setState(() {
        _isRecording = false;
        _audioPath = path;
      });

      // 停止脉冲动画
      _pulseAnimationController.stop();
      _pulseAnimationController.reset();

      // 如果录音成功，自动处理音频文件
      if (path != null) {
        await _processMediaFile(path, isAudio: true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('录音停止失败: $e')),
        );
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      await _requestPermissions();

      final XFile? image = await _imagePicker.pickImage(source: source);
      if (image != null) {
        final directory = await getExternalStorageDirectory();
        final imageDir = Directory('${directory!.path}/CloudNote/Images');
        if (!await imageDir.exists()) {
          await imageDir.create(recursive: true);
        }

        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final extension = image.path.split('.').last;
        final newPath = '${imageDir.path}/image_$timestamp.$extension';

        await File(image.path).copy(newPath);
        setState(() {
          _imagePath = newPath;
        });

        // 自动处理图片文件
        await _processMediaFile(newPath, isAudio: false);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('图片选择失败: $e')),
        );
      }
    }
  }



  Future<void> _processMediaFile(String filePath, {required bool isAudio}) async {
    if (!mounted) return;

    setState(() {
      _isUploading = true;
    });

    try {
      // 第一步：上传到文件床
      final authService = Provider.of<AuthService>(context, listen: false);
      final uploadService = FilehostUploadService(authService);
      uploadService.init();

      final file = File(filePath);
      final uploadResult = await uploadService.uploadFile(file);

      if (!uploadResult.success || uploadResult.url == null) {
        throw Exception(uploadResult.error ?? '文件上传失败');
      }

      // 更新UI显示上传结果
      setState(() {
        _uploadedFileUrl = uploadResult.url;
        _isUploading = false;
        _isProcessing = true;
      });

      // 第二步：AI识别
      if (!mounted) return;
      final aiProvider = Provider.of<AiProvider>(context, listen: false);

      final extractedText = await MediaProcessingService.processMediaFile(
        filePath: filePath,
        apiKey: aiProvider.apiKey,
        model: aiProvider.model,
        baseUrl: aiProvider.baseUrl,
      );

      if (extractedText != null && mounted) {
        setState(() {
          _aiRecognitionResult = extractedText;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${isAudio ? "录音" : "图片"}上传并识别成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${isAudio ? "录音" : "图片"}处理失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
          _isProcessing = false;
        });

        // 清理临时文件
        try {
          final file = File(filePath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          debugPrint('Failed to delete temp file: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AlertDialog(
      title: const Text('快速导入'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [


            // 三个内容框区域
            _buildContentBoxes(theme, colorScheme),

            const SizedBox(height: 16),
            // 媒体操作按钮区域
            _buildMediaButtons(theme, colorScheme),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: (_isLoading || _isProcessing || _isUploading) ? null : () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
      ],
    );
  }

  // 构建三个内容框
  Widget _buildContentBoxes(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      children: [
        // 1. 原输入框
        _buildInputBox(theme, colorScheme),

        // 2. URL框（如果有上传的文件）
        if (_uploadedFileUrl != null) ...[
          const SizedBox(height: 8),
          _buildUrlBox(theme, colorScheme),
        ],

        // 3. AI识别框（如果有识别结果）
        if (_aiRecognitionResult != null) ...[
          const SizedBox(height: 8),
          _buildAiResultBox(theme, colorScheme),
        ],
      ],
    );
  }

  // 构建输入框
  Widget _buildInputBox(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.edit, size: 16, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '输入内容',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                _buildActionButtons(
                  onCopy: () => _copyInputContent(),
                  onClear: () => _clearInputContent(),
                  onImport: () => _importInputContent(),
                  colorScheme: colorScheme,
                ),
              ],
            ),
          ),
          // 输入框内容
          Container(
            constraints: const BoxConstraints(maxHeight: 120),
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: const InputDecoration(
                hintText: '输入或粘贴要导入的内容',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(12),
              ),
              maxLines: null,
              scrollPhysics: const BouncingScrollPhysics(),
            ),
          ),
        ],
      ),
    );
  }

  // 构建URL框
  Widget _buildUrlBox(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.link, size: 16, color: colorScheme.secondary),
                const SizedBox(width: 8),
                Text(
                  '文件链接',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: colorScheme.secondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                _buildActionButtons(
                  onCopy: () => _copyUrlContent(),
                  onClear: () => _clearUrlContent(),
                  onImport: () => _importUrlContent(),
                  colorScheme: colorScheme,
                ),
              ],
            ),
          ),
          // URL内容
          Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: SelectableText(
                _uploadedFileUrl ?? '',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建AI识别结果框
  Widget _buildAiResultBox(ThemeData theme, ColorScheme colorScheme) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.smart_toy, size: 16, color: colorScheme.tertiary),
                const SizedBox(width: 8),
                Text(
                  'AI识别结果',
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: colorScheme.tertiary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                _buildActionButtons(
                  onCopy: () => _copyAiContent(),
                  onClear: () => _clearAiContent(),
                  onImport: () => _importAiContent(),
                  colorScheme: colorScheme,
                ),
              ],
            ),
          ),
          // AI识别内容
          Container(
            constraints: const BoxConstraints(maxHeight: 120),
            padding: const EdgeInsets.all(12),
            child: SingleChildScrollView(
              child: SelectableText(
                _aiRecognitionResult ?? '',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建媒体操作按钮
  Widget _buildMediaButtons(ThemeData theme, ColorScheme colorScheme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 相册按钮
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: (_isProcessing || _isUploading)
                ? colorScheme.primaryContainer.withValues(alpha: 0.5)
                : colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(16),
          ),
          child: InkWell(
            onTap: (_isProcessing || _isLoading || _isUploading) ? null : () => _pickImage(ImageSource.gallery),
            borderRadius: BorderRadius.circular(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.photo_library,
                  color: (_isProcessing || _isUploading)
                      ? colorScheme.onPrimaryContainer.withValues(alpha: 0.5)
                      : colorScheme.onPrimaryContainer,
                  size: 24,
                ),
                const SizedBox(height: 2),
                Text(
                  '相册',
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: (_isProcessing || _isUploading)
                        ? colorScheme.onPrimaryContainer.withValues(alpha: 0.5)
                        : colorScheme.onPrimaryContainer,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),

        // 拍照按钮
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: (_isProcessing || _isUploading)
                ? colorScheme.primaryContainer.withValues(alpha: 0.5)
                : colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(16),
          ),
          child: InkWell(
            onTap: (_isProcessing || _isLoading || _isUploading) ? null : () => _pickImage(ImageSource.camera),
            borderRadius: BorderRadius.circular(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.camera_alt,
                  color: (_isProcessing || _isUploading)
                      ? colorScheme.onPrimaryContainer.withValues(alpha: 0.5)
                      : colorScheme.onPrimaryContainer,
                  size: 24,
                ),
                const SizedBox(height: 2),
                Text(
                  '拍照',
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: (_isProcessing || _isUploading)
                        ? colorScheme.onPrimaryContainer.withValues(alpha: 0.5)
                        : colorScheme.onPrimaryContainer,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),

        // 录音按钮
        GestureDetector(
          onLongPressStart: (_isProcessing || _isLoading || _isUploading) ? null : (_) => _startRecording(),
          onLongPressEnd: (_isProcessing || _isLoading || _isUploading) ? null : (_) => _stopRecording(),
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isRecording ? _pulseAnimation.value : 1.0,
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: (_isProcessing || _isUploading)
                        ? colorScheme.primaryContainer.withValues(alpha: 0.5)
                        : _isRecording
                            ? colorScheme.error
                            : colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: _isRecording ? [
                      BoxShadow(
                        color: colorScheme.error.withValues(alpha: 0.4),
                        blurRadius: 12,
                        spreadRadius: 2,
                      ),
                    ] : null,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _isRecording ? Icons.stop : Icons.mic,
                        color: (_isProcessing || _isUploading)
                            ? colorScheme.onPrimaryContainer.withValues(alpha: 0.5)
                            : _isRecording
                                ? colorScheme.onError
                                : colorScheme.onPrimaryContainer,
                        size: 24,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '录音',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: (_isProcessing || _isUploading)
                              ? colorScheme.onPrimaryContainer.withValues(alpha: 0.5)
                              : _isRecording
                                  ? colorScheme.onError
                                  : colorScheme.onPrimaryContainer,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // 构建功能组合按钮
  Widget _buildActionButtons({
    required VoidCallback onCopy,
    required VoidCallback onClear,
    required VoidCallback onImport,
    required ColorScheme colorScheme,
  }) {
    final isDisabled = _isLoading || _isProcessing || _isUploading;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 复制按钮
        SizedBox(
          width: 28,
          height: 28,
          child: IconButton(
            onPressed: isDisabled ? null : onCopy,
            icon: const Icon(Icons.copy, size: 14),
            tooltip: '复制',
            style: IconButton.styleFrom(
              padding: EdgeInsets.zero,
              backgroundColor: colorScheme.surface,
              foregroundColor: colorScheme.onSurface,
            ),
          ),
        ),
        const SizedBox(width: 4),
        // 清空按钮
        SizedBox(
          width: 28,
          height: 28,
          child: IconButton(
            onPressed: isDisabled ? null : onClear,
            icon: const Icon(Icons.clear, size: 14),
            tooltip: '清空',
            style: IconButton.styleFrom(
              padding: EdgeInsets.zero,
              backgroundColor: colorScheme.surface,
              foregroundColor: colorScheme.onSurface,
            ),
          ),
        ),
        const SizedBox(width: 4),
        // 导入按钮
        SizedBox(
          width: 28,
          height: 28,
          child: IconButton(
            onPressed: isDisabled ? null : onImport,
            icon: const Icon(Icons.download, size: 14),
            tooltip: '导入到快速笔记',
            style: IconButton.styleFrom(
              padding: EdgeInsets.zero,
              backgroundColor: colorScheme.surface,
              foregroundColor: colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  // 输入框相关操作
  void _copyInputContent() {
    final content = _controller.text.trim();
    if (content.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有内容可复制'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Clipboard.setData(ClipboardData(text: content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('输入内容已复制到剪切板'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _clearInputContent() {
    setState(() {
      _controller.clear();
      _controller.selection = const TextSelection.collapsed(offset: 0);
    });
    _focusNode.requestFocus();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('输入内容已清空'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _importInputContent() {
    // 输入框内容导入到快速笔记
    final content = _controller.text.trim();
    if (content.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('输入框没有内容可导入'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    _quickImportContent(content, '输入内容');
  }

  // URL框相关操作
  void _copyUrlContent() {
    if (_uploadedFileUrl == null || _uploadedFileUrl!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有链接可复制'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Clipboard.setData(ClipboardData(text: _uploadedFileUrl!));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('文件链接已复制到剪切板'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _clearUrlContent() {
    setState(() {
      _uploadedFileUrl = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('文件链接已清空'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _importUrlContent() {
    if (_uploadedFileUrl == null || _uploadedFileUrl!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有链接可导入'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // 生成markdown格式的链接
    final fileName = _getFileNameFromUrl(_uploadedFileUrl!);
    final markdownLink = '[$fileName]($_uploadedFileUrl!)';

    _quickImportContent(markdownLink, '文件链接');
  }

  // 从URL中提取文件名
  String _getFileNameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        final fileName = pathSegments.last;
        if (fileName.isNotEmpty) {
          return fileName;
        }
      }
      // 如果无法提取文件名，使用默认名称
      return '附件';
    } catch (e) {
      return '附件';
    }
  }

  // AI识别结果相关操作
  void _copyAiContent() {
    if (_aiRecognitionResult == null || _aiRecognitionResult!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有识别结果可复制'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Clipboard.setData(ClipboardData(text: _aiRecognitionResult!));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AI识别结果已复制到剪切板'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _clearAiContent() {
    setState(() {
      _aiRecognitionResult = null;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('AI识别结果已清空'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 1),
      ),
    );
  }

  void _importAiContent() {
    if (_aiRecognitionResult == null || _aiRecognitionResult!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有识别结果可导入'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    _quickImportContent(_aiRecognitionResult!, 'AI识别结果');
  }

  // 通用的快速导入方法
  Future<void> _quickImportContent(String content, String contentType) async {
    if (content.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$contentType没有内容可导入'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final provider = context.read<MarkdownProvider>();
      final result = await provider.quickImportToFile(
        filename: '快速笔记.md',
        content: content,
      );

      if (result != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$contentType已导入到快速笔记.md'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('导入失败，请重试'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('导入失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
