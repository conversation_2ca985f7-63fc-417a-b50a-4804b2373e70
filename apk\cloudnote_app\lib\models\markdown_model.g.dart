// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'markdown_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class MarkdownModelAdapter extends TypeAdapter<MarkdownModel> {
  @override
  final int typeId = 7;

  @override
  MarkdownModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return MarkdownModel(
      id: fields[0] as String,
      filename: fields[1] as String,
      content: fields[2] as String,
      contentHash: fields[3] as String,
      fileSize: fields[4] as int,
      versionNumber: fields[5] as int,
      createdAt: fields[6] as int,
      updatedAt: fields[7] as int,
      lastSyncAt: fields[8] as int?,
      deviceId: fields[9] as String?,
      isDeleted: fields[10] as bool,
      deletedAt: fields[11] as int?,
      syncStatus: fields[12] as SyncStatus,
      isModified: fields[13] as bool,
      filePath: fields[14] as String? ?? '',
      fullPath: fields[15] as String? ?? fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, MarkdownModel obj) {
    writer
      ..writeByte(16)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.filename)
      ..writeByte(2)
      ..write(obj.content)
      ..writeByte(3)
      ..write(obj.contentHash)
      ..writeByte(4)
      ..write(obj.fileSize)
      ..writeByte(5)
      ..write(obj.versionNumber)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt)
      ..writeByte(8)
      ..write(obj.lastSyncAt)
      ..writeByte(9)
      ..write(obj.deviceId)
      ..writeByte(10)
      ..write(obj.isDeleted)
      ..writeByte(11)
      ..write(obj.deletedAt)
      ..writeByte(12)
      ..write(obj.syncStatus)
      ..writeByte(13)
      ..write(obj.isModified)
      ..writeByte(14)
      ..write(obj.filePath)
      ..writeByte(15)
      ..write(obj.fullPath);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MarkdownModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
