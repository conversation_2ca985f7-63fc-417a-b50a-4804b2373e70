import 'package:flutter/foundation.dart';
import '../models/markdown_model.dart';
import '../models/tab_model.dart';
import '../services/markdown_service.dart';
import '../services/markdown_sync_service.dart';

/// Markdown文件管理Provider
class MarkdownProvider extends ChangeNotifier {
  final MarkdownService _markdownService;
  final MarkdownSyncService _syncService;

  List<MarkdownModel> _files = [];
  MarkdownModel? _activeFile;
  bool _isLoading = false;
  bool _isSyncing = false;
  String? _error;
  bool _isInitialized = false;

  MarkdownProvider(this._markdownService, this._syncService);

  // Getters
  List<MarkdownModel> get files => _files;
  MarkdownModel? get activeFile => _activeFile;
  bool get isLoading => _isLoading;
  bool get isSyncing => _isSyncing;
  String? get error => _error;
  int get fileCount => _files.length;
  bool get isInitialized => _isInitialized;

  /// 初始化Provider
  Future<void> init() async {
    // 避免重复初始化
    if (_isInitialized) {
      debugPrint('MarkdownProvider already initialized, skipping...');
      return;
    }

    try {
      _setLoading(true);
      await _markdownService.init();
      await loadFiles();
      _isInitialized = true;
      debugPrint('MarkdownProvider initialized successfully');
    } catch (e) {
      _setError('初始化失败: $e');
      debugPrint('Failed to initialize MarkdownProvider: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 加载所有文件
  Future<void> loadFiles() async {
    try {
      _files = _markdownService.getAllFiles();
      _clearError();
      notifyListeners();
      debugPrint('Loaded ${_files.length} markdown files');
    } catch (e) {
      _setError('加载文件失败: $e');
      debugPrint('Failed to load files: $e');
    }
  }

  /// 创建新文件
  Future<MarkdownModel?> createFile({
    required String filename,
    String content = '',
  }) async {
    try {
      _setLoading(true);

      // 确保文件名以.md结尾
      final normalizedFilename = filename.endsWith('.md') ? filename : '$filename.md';

      final file = await _markdownService.createFile(
        filename: normalizedFilename,
        content: content,
      );

      await loadFiles();
      _setActiveFile(file);
      _clearError();

      debugPrint('Created new file: ${file.fullPath}');
      return file;
    } catch (e) {
      _setError('创建文件失败: $e');
      debugPrint('Failed to create file: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// 打开文件
  void openFile(MarkdownModel file) {
    _setActiveFile(file);
    debugPrint('Opened file: ${file.filename}');
  }

  /// 更新文件内容
  Future<void> updateFileContent(String fileId, String content) async {
    try {
      await _markdownService.updateFileContent(fileId, content);
      
      // 更新本地列表中的文件
      final fileIndex = _files.indexWhere((f) => f.id == fileId);
      if (fileIndex != -1) {
        _files[fileIndex] = _markdownService.getFileById(fileId)!;
        
        // 如果是当前活动文件，也更新活动文件
        if (_activeFile?.id == fileId) {
          _activeFile = _files[fileIndex];
        }
        
        notifyListeners();
      }
      
      _clearError();
      debugPrint('Updated file content: $fileId');
    } catch (e) {
      _setError('更新文件失败: $e');
      debugPrint('Failed to update file content: $e');
    }
  }

  /// 重命名文件
  Future<bool> renameFile(String fileId, String newPath) async {
    try {
      _setLoading(true);

      // 确保路径以.md结尾
      final normalizedPath = newPath.endsWith('.md') ? newPath : '$newPath.md';

      await _markdownService.renameFile(fileId, normalizedPath);
      await loadFiles();

      // 如果重命名的是当前活动文件，更新活动文件引用
      if (_activeFile?.id == fileId) {
        _activeFile = _markdownService.getFileById(fileId);
      }

      _clearError();
      debugPrint('Renamed file: $fileId -> $normalizedPath');
      return true;
    } catch (e) {
      _setError('重命名文件失败: $e');
      debugPrint('Failed to rename file: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 删除文件
  Future<bool> deleteFile(String fileId) async {
    try {
      _setLoading(true);
      await _markdownService.deleteFile(fileId);
      await loadFiles();
      
      // 如果删除的是当前活动文件，清除活动文件
      if (_activeFile?.id == fileId) {
        _activeFile = null;
      }
      
      _clearError();
      debugPrint('Deleted file: $fileId');
      return true;
    } catch (e) {
      _setError('删除文件失败: $e');
      debugPrint('Failed to delete file: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 同步所有文件
  Future<bool> syncAllFiles() async {
    if (_isSyncing) {
      debugPrint('Sync already in progress');
      return false;
    }

    try {
      _setSyncing(true);

      // 记录当前活动文件的ID
      final activeFileId = _activeFile?.id;

      final success = await _syncService.syncAllFiles();

      if (success) {
        await loadFiles();

        // 如果之前有活动文件，尝试重新设置并刷新
        if (activeFileId != null) {
          await _refreshActiveFile(activeFileId);
        }

        _clearError();
        debugPrint('Sync completed successfully');
      } else {
        _setError('同步失败');
        debugPrint('Sync failed');
      }

      return success;
    } catch (e) {
      _setError('同步失败: $e');
      debugPrint('Sync error: $e');
      return false;
    } finally {
      _setSyncing(false);
    }
  }

  /// 刷新当前活动文件
  Future<void> _refreshActiveFile(String fileId) async {
    try {
      // 从服务中获取最新的文件数据
      final updatedFile = _markdownService.getFileById(fileId);

      if (updatedFile != null) {
        // 检查文件内容是否发生变化
        final contentChanged = _activeFile?.content != updatedFile.content;
        final versionChanged = _activeFile?.versionNumber != updatedFile.versionNumber;
        final syncStatusChanged = _activeFile?.syncStatus != updatedFile.syncStatus;

        if (contentChanged || versionChanged || syncStatusChanged) {
          debugPrint('Active file updated after sync: ${updatedFile.filename}');
          debugPrint('  Content changed: $contentChanged');
          debugPrint('  Version changed: $versionChanged (${_activeFile?.versionNumber} -> ${updatedFile.versionNumber})');
          debugPrint('  Sync status changed: $syncStatusChanged (${_activeFile?.syncStatus} -> ${updatedFile.syncStatus})');

          // 更新活动文件
          _activeFile = updatedFile;

          // 强制通知UI更新
          debugPrint('Notifying listeners of active file change...');
          notifyListeners();

          // 额外的延迟通知，确保UI能收到更新
          Future.delayed(const Duration(milliseconds: 100), () {
            debugPrint('Delayed notification for active file change...');
            notifyListeners();
          });
        }
      } else {
        // 文件可能被删除了
        debugPrint('Active file no longer exists after sync: $fileId');
        _activeFile = null;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error refreshing active file: $e');
    }
  }

  /// 获取需要同步的文件数量
  int get filesNeedingSync {
    return _files.where((file) => file.syncStatus != SyncStatus.synced).length;
  }

  /// 检查是否有未同步的更改
  bool get hasUnsyncedChanges {
    return _files.any((file) => file.syncStatus != SyncStatus.synced);
  }

  /// 获取冲突文件
  List<MarkdownModel> get conflictFiles {
    return _files.where((file) => file.syncStatus == SyncStatus.conflict).toList();
  }

  /// 解决冲突 - 使用本地版本
  Future<bool> resolveConflictWithLocal(String fileId) async {
    try {
      final success = await _syncService.forceUploadFile(fileId);
      if (success) {
        await loadFiles();
        _clearError();
        debugPrint('Resolved conflict with local version: $fileId');
      }
      return success;
    } catch (e) {
      _setError('解决冲突失败: $e');
      debugPrint('Failed to resolve conflict: $e');
      return false;
    }
  }

  /// 解决冲突 - 使用服务器版本
  Future<bool> resolveConflictWithServer(String fileId) async {
    try {
      final file = _markdownService.getFileById(fileId);
      if (file == null) return false;

      // 标记为需要下载，然后同步
      file.syncStatus = SyncStatus.needsDownload;
      await file.save();
      
      final success = await _syncService.syncAllFiles();
      if (success) {
        await loadFiles();
        _clearError();
        debugPrint('Resolved conflict with server version: $fileId');
      }
      return success;
    } catch (e) {
      _setError('解决冲突失败: $e');
      debugPrint('Failed to resolve conflict: $e');
      return false;
    }
  }

  /// 搜索文件
  List<MarkdownModel> searchFiles(String query) {
    if (query.isEmpty) return _files;
    
    final lowerQuery = query.toLowerCase();
    return _files.where((file) {
      return file.filename.toLowerCase().contains(lowerQuery) ||
             file.content.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  /// 按修改时间排序文件
  void sortFilesByModified({bool ascending = false}) {
    _files.sort((a, b) {
      return ascending 
          ? a.updatedAt.compareTo(b.updatedAt)
          : b.updatedAt.compareTo(a.updatedAt);
    });
    notifyListeners();
  }

  /// 按文件名排序文件
  void sortFilesByName({bool ascending = true}) {
    _files.sort((a, b) {
      return ascending
          ? a.filename.compareTo(b.filename)
          : b.filename.compareTo(a.filename);
    });
    notifyListeners();
  }

  /// 快速导入内容到指定文件
  Future<MarkdownModel?> quickImportToFile({
    required String filename,
    required String content,
  }) async {
    try {
      _setLoading(true);

      // 确保文件名以.md结尾
      final normalizedFilename = filename.endsWith('.md') ? filename : '$filename.md';

      // 查找现有文件（支持路径匹配）
      MarkdownModel? existingFile = _files
          .where((file) => file.fullPath == normalizedFilename && !file.isDeleted)
          .firstOrNull;

      if (existingFile != null) {
        // 文件存在，追加内容
        final newContent = existingFile.content.isEmpty
            ? content
            : '${existingFile.content}\n\n$content';

        await updateFileContent(existingFile.id, newContent);
        _setActiveFile(existingFile);
        debugPrint('Appended content to existing file: $normalizedFilename');
        return existingFile;
      } else {
        // 文件不存在，创建新文件
        final newFile = await createFile(
          filename: normalizedFilename,
          content: content,
        );
        debugPrint('Created new file with imported content: $normalizedFilename');
        return newFile;
      }
    } catch (e) {
      _setError('快速导入失败: $e');
      debugPrint('Failed to quick import: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// 设置活动文件
  void _setActiveFile(MarkdownModel? file) {
    _activeFile = file;
    notifyListeners();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置同步状态
  void _setSyncing(bool syncing) {
    _isSyncing = syncing;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _markdownService.dispose();
    super.dispose();
  }
}
