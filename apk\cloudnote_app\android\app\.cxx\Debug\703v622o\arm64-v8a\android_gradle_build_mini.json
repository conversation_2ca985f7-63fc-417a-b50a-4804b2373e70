{"buildFiles": ["D:\\Tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\android\\app\\.cxx\\Debug\\703v622o\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Tools\\android_sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\android\\cloudnote\\flutter_app\\apk\\cloudnote_app\\android\\app\\.cxx\\Debug\\703v622o\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}