package com.cloudnote.app.cloudnote_app

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.cloudnote.app/shortcuts"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 启用边到边显示
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // 设置系统UI样式
        setupSystemUI()

        // 处理shortcuts intent
        handleShortcutIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleShortcutIntent(intent)
    }

    private fun setupSystemUI() {
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)

        // 设置状态栏和导航栏为透明
        window.statusBarColor = android.graphics.Color.TRANSPARENT
        window.navigationBarColor = android.graphics.Color.TRANSPARENT

        // 根据系统主题设置状态栏和导航栏图标颜色
        val isLightTheme = (resources.configuration.uiMode and
            android.content.res.Configuration.UI_MODE_NIGHT_MASK) !=
            android.content.res.Configuration.UI_MODE_NIGHT_YES

        windowInsetsController.isAppearanceLightStatusBars = isLightTheme
        windowInsetsController.isAppearanceLightNavigationBars = isLightTheme

        // 禁用系统强制对比度
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            window.isNavigationBarContrastEnforced = false
            window.isStatusBarContrastEnforced = false
        }
    }

    private fun handleShortcutIntent(intent: Intent?) {
        android.util.Log.d("MainActivity", "handleShortcutIntent called")
        intent?.let {
            val shortcutAction = it.getStringExtra("shortcut_action")
            android.util.Log.d("MainActivity", "Shortcut action: $shortcutAction")
            shortcutAction?.let { action ->
                // 设置Flutter引擎准备就绪后的回调
                flutterEngine?.let { engine ->
                    android.util.Log.d("MainActivity", "Flutter engine ready, sending action: $action")
                    // 延迟发送，确保Flutter完全初始化
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        MethodChannel(engine.dartExecutor.binaryMessenger, CHANNEL).invokeMethod(
                            "handleShortcut",
                            mapOf("action" to action)
                        )
                    }, 500) // 延迟500ms
                } ?: run {
                    // 如果Flutter引擎还没准备好，保存action等待后续处理
                    android.util.Log.d("MainActivity", "Flutter engine not ready, saving action: $action")
                    pendingShortcutAction = action
                }
            }
        }
    }

    override fun configureFlutterEngine(flutterEngine: io.flutter.embedding.engine.FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        android.util.Log.d("MainActivity", "configureFlutterEngine called")

        // 设置MethodChannel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            android.util.Log.d("MainActivity", "MethodChannel call: ${call.method}")
            when (call.method) {
                "getPendingShortcut" -> {
                    android.util.Log.d("MainActivity", "Returning pending shortcut: $pendingShortcutAction")
                    result.success(pendingShortcutAction)
                    pendingShortcutAction = null
                }
                else -> result.notImplemented()
            }
        }

        // 如果有待处理的shortcut action，延迟发送
        pendingShortcutAction?.let { action ->
            android.util.Log.d("MainActivity", "Sending pending shortcut action: $action")
            // 延迟发送，确保Flutter完全初始化
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).invokeMethod(
                    "handleShortcut",
                    mapOf("action" to action)
                )
            }, 1000) // 延迟1秒
            pendingShortcutAction = null
        }
    }

    companion object {
        private var pendingShortcutAction: String? = null
    }
}
