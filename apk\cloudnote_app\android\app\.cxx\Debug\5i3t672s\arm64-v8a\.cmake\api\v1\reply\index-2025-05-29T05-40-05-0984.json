{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Tools/android_sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Tools/android_sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Tools/android_sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Tools/android_sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-b035042fbef67cc5cfb7.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-38929a119c7c0cf81e4e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ab5c6c3938999204bfe7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-38929a119c7c0cf81e4e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-ab5c6c3938999204bfe7.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-b035042fbef67cc5cfb7.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}