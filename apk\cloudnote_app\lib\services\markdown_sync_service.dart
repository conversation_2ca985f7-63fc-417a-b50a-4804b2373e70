import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/markdown_model.dart';
import '../models/tab_model.dart';
import '../services/auth_service.dart';
import '../services/markdown_service.dart';

/// Markdown文件云端同步服务
class MarkdownSyncService {
  final AuthService _authService;
  final MarkdownService _markdownService;

  bool _isSyncing = false;

  // 跟踪本次同步中上传的文件，避免误删
  final Set<String> _uploadedInCurrentSync = {};

  MarkdownSyncService(this._authService, this._markdownService);

  /// 是否正在同步
  bool get isSyncing => _isSyncing;

  /// 同步所有文件
  Future<bool> syncAllFiles() async {
    if (_isSyncing) {
      debugPrint('Markdown sync already in progress');
      return false;
    }

    if (!_authService.isAuthenticated()) {
      debugPrint('Not authenticated, skipping markdown sync');
      return false;
    }

    _isSyncing = true;

    try {
      debugPrint('Starting markdown files sync...');

      // 清空本次同步的上传跟踪
      _uploadedInCurrentSync.clear();

      // 1. 获取服务器文件列表
      final serverFiles = await _getServerFilesList();
      if (serverFiles == null) {
        debugPrint('Failed to get server files list');
        return false;
      }

      // 2. 获取本地需要同步的文件
      final localFiles = _markdownService.getFilesNeedingSync();
      debugPrint('Found ${localFiles.length} local files needing sync');

      // 3. 上传本地修改的文件
      for (final localFile in localFiles) {
        debugPrint('Processing local file: ${localFile.fullPath}, status: ${localFile.syncStatus}');
        if (localFile.syncStatus == SyncStatus.needsUpload) {
          final success = await _uploadFile(localFile);
          if (success) {
            _uploadedInCurrentSync.add(localFile.fullPath);
          }
          debugPrint('Upload result for ${localFile.fullPath}: $success');
        }
      }

      // 4. 下载服务器新文件或更新的文件
      debugPrint('Found ${serverFiles.length} server files to process');
      for (final serverFile in serverFiles) {
        debugPrint('Processing server file: ${serverFile['filename']}');
        await _processServerFile(serverFile);
      }

      // 5. 处理删除的文件
      await _processDeletedFiles();

      // 6. 检查并清理本地孤立文件（服务器上不存在的文件）
      await _cleanupOrphanedFiles(serverFiles);

      debugPrint('Markdown sync completed successfully');
      return true;
    } catch (e) {
      debugPrint('Markdown sync failed: $e');
      return false;
    } finally {
      _isSyncing = false;
    }
  }

  /// 获取服务器文件列表
  Future<List<Map<String, dynamic>>?> _getServerFilesList() async {
    try {
      final auth = _authService.getCurrentAuth();
      if (auth == null) return null;

      final url = '${auth.serverUrl}api/markdown-sync.php?action=list';
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': _getDeviceId(),
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return List<Map<String, dynamic>>.from(data['files'] ?? []);
        } else {
          debugPrint('Server error: ${data['error']}');
          return null;
        }
      } else {
        debugPrint('HTTP error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Error getting server files list: $e');
      return null;
    }
  }

  /// 上传文件到服务器
  Future<bool> _uploadFile(MarkdownModel file) async {
    try {
      debugPrint('Uploading file: ${file.filename}');

      final auth = _authService.getCurrentAuth();
      if (auth == null) return false;

      final url = '${auth.serverUrl}api/markdown-sync.php';
      final requestBody = {
        'action': file.isDeleted ? 'delete' : 'sync',
        'filename': file.fullPath, // 使用完整路径
        'content': file.isDeleted ? '' : file.content,
        'timestamp': file.updatedAt,
        'version': file.versionNumber,
        'force': false,
      };

      debugPrint('Uploading to URL: $url');
      debugPrint('Request body: ${json.encode(requestBody)}');

      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': _getDeviceId(),
        },
        body: json.encode(requestBody),
      );

      debugPrint('Upload response status: ${response.statusCode}');
      debugPrint('Upload response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['success'] == true) {
          if (file.isDeleted) {
            // 删除操作成功同步，从本地数据库中移除记录
            await _markdownService.removeFileFromDatabase(file.id);
            debugPrint('Successfully deleted and cleaned up: ${file.filename}');
          } else {
            // 普通文件上传成功，更新本地状态
            file.markAsSynced(
              serverVersion: data['version_number'],
              serverTimestamp: data['server_timestamp'],
            );
            // 记录本次同步中上传的文件
            _uploadedInCurrentSync.add(file.filename);
            debugPrint('Successfully uploaded: ${file.filename}');
          }
          return true;
        } else if (data['conflict'] == true) {
          // 处理冲突
          debugPrint('Conflict detected for: ${file.filename}');
          await _handleConflict(file, data);
          return false;
        } else {
          debugPrint('Upload failed: ${data['error'] ?? 'Unknown error'}');
          return false;
        }
      } else {
        debugPrint('HTTP error uploading file: ${response.statusCode}, body: ${response.body}');
        return false;
      }
    } catch (e) {
      debugPrint('Error uploading file ${file.filename}: $e');
      return false;
    }
  }

  /// 处理服务器文件
  Future<void> _processServerFile(Map<String, dynamic> serverFile) async {
    try {
      final fullPath = serverFile['full_path'] as String? ?? serverFile['filename'] as String;
      final serverTimestamp = serverFile['updated_at'] as int;
      final isDeleted = serverFile['is_deleted'] == 1;

      final localFile = _markdownService.getFileByPath(fullPath);

      if (isDeleted) {
        // 服务器文件已删除，需要删除本地文件
        if (localFile != null) {
          await _deleteLocalFile(localFile);
          debugPrint('Deleted local file due to server deletion: $fullPath');
        }
        return;
      }

      if (localFile == null) {
        // 本地没有此文件，下载
        await _downloadFile(fullPath);
      } else if (localFile.updatedAt < serverTimestamp) {
        // 服务器版本更新，下载
        if (localFile.syncStatus == SyncStatus.needsUpload) {
          // 本地也有修改，产生冲突
          await _handleServerConflict(localFile, serverFile);
        } else {
          await _downloadFile(fullPath);
        }
      }
    } catch (e) {
      debugPrint('Error processing server file: $e');
    }
  }

  /// 下载文件内容
  Future<bool> _downloadFile(String fullPath) async {
    try {
      debugPrint('Downloading file: $fullPath');

      final auth = _authService.getCurrentAuth();
      if (auth == null) return false;

      final url = '${auth.serverUrl}api/markdown-sync.php?action=get&filename=${Uri.encodeComponent(fullPath)}';
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': _getDeviceId(),
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['success'] == true) {
          final fileData = data['file'];
          final localFile = _markdownService.getFileByPath(fullPath);
          
          if (localFile != null) {
            // 更新现有文件
            final oldContent = localFile.content;
            final oldVersion = localFile.versionNumber;

            localFile.content = fileData['content'];
            localFile.contentHash = fileData['content_hash'];
            localFile.fileSize = fileData['file_size'];
            localFile.versionNumber = fileData['version_number'];
            localFile.updatedAt = fileData['updated_at'];
            localFile.markAsSynced();

            debugPrint('Updated local file: ${localFile.filename}');
            debugPrint('  Content changed: ${oldContent != localFile.content}');
            debugPrint('  Version: $oldVersion -> ${localFile.versionNumber}');

            // 保存到文件系统
            await _markdownService.saveModelToFile(localFile);
          } else {
            // 创建新文件 - 直接创建模型并保存
            final newFile = MarkdownModel.fromJson(fileData);
            newFile.syncStatus = SyncStatus.synced;
            newFile.isModified = false;

            // 保存到数据库
            await _markdownService.saveModelToDatabase(newFile);

            // 保存到文件系统
            await _markdownService.saveModelToFile(newFile);
          }
          
          debugPrint('Successfully downloaded: $fullPath');
          return true;
        } else {
          debugPrint('Download failed: ${data['error']}');
          return false;
        }
      } else {
        debugPrint('HTTP error downloading file: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('Error downloading file $fullPath: $e');
      return false;
    }
  }

  /// 处理冲突
  Future<void> _handleConflict(MarkdownModel localFile, Map<String, dynamic> conflictData) async {
    // 简单的冲突处理：标记为冲突状态，让用户手动解决
    localFile.syncStatus = SyncStatus.conflict;
    await localFile.save();
    debugPrint('Marked file as conflict: ${localFile.filename}');
  }

  /// 处理服务器冲突
  Future<void> _handleServerConflict(MarkdownModel localFile, Map<String, dynamic> serverFile) async {
    // 简单的冲突处理：标记为冲突状态
    localFile.syncStatus = SyncStatus.conflict;
    await localFile.save();
    debugPrint('Server conflict detected for: ${localFile.filename}');
  }

  /// 处理删除的文件
  Future<void> _processDeletedFiles() async {
    final deletedFiles = _markdownService.getDeletedFilesNeedingSync();

    for (final file in deletedFiles) {
      await _uploadFile(file);
    }

    // 清理已成功同步的删除文件
    await _cleanupDeletedFiles();
  }

  /// 删除本地文件（物理文件和数据库记录）
  Future<void> _deleteLocalFile(MarkdownModel file) async {
    try {
      // 删除物理文件
      await _markdownService.deletePhysicalFile(file.fullPath);

      // 从数据库中删除记录
      await _markdownService.removeFileFromDatabase(file.id);

      debugPrint('Completely deleted local file: ${file.filename}');
    } catch (e) {
      debugPrint('Error deleting local file ${file.filename}: $e');
    }
  }

  /// 清理已成功同步的删除文件
  Future<void> _cleanupDeletedFiles() async {
    try {
      final deletedFiles = _markdownService.getAllDeletedFiles();

      for (final file in deletedFiles) {
        if (file.syncStatus == SyncStatus.synced) {
          // 已同步的删除文件可以从数据库中移除
          await _markdownService.removeFileFromDatabase(file.id);
          debugPrint('Cleaned up synced deleted file: ${file.filename}');
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up deleted files: $e');
    }
  }

  /// 清理本地孤立文件（服务器上不存在的文件）
  Future<void> _cleanupOrphanedFiles(List<Map<String, dynamic>> serverFiles) async {
    try {
      // 获取服务器上所有活跃文件的完整路径
      final serverFilePaths = serverFiles
          .where((file) => file['is_deleted'] != 1)
          .map((file) => (file['full_path'] as String?) ?? (file['filename'] as String))
          .toSet();

      // 获取所有本地文件（包括已删除的）
      final localFiles = _markdownService.getAllFiles(includeDeleted: true);

      for (final localFile in localFiles) {
        // 只清理满足以下条件的文件：
        // 1. 本地文件在服务器上不存在
        // 2. 文件状态是已同步（说明之前同步过）
        // 3. 文件不是刚刚在本次同步中上传的（避免误删新上传的文件）
        if (!serverFilePaths.contains(localFile.fullPath) &&
            localFile.syncStatus == SyncStatus.synced &&
            !_uploadedInCurrentSync.contains(localFile.fullPath)) {
          // 这个文件在服务器上已被删除，但本地同步状态还是已同步
          // 说明是在其他设备上删除的，需要在本地也删除
          await _deleteLocalFile(localFile);
          debugPrint('Cleaned up orphaned file: ${localFile.fullPath}');
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up orphaned files: $e');
    }
  }

  /// 获取设备ID
  String _getDeviceId() {
    // 生成一个固定的设备ID，基于应用标识
    return 'flutter_app_${DateTime.now().millisecondsSinceEpoch ~/ 86400000}'; // 每天一个ID
  }

  /// 强制上传文件（解决冲突时使用）
  Future<bool> forceUploadFile(String fileId) async {
    final file = _markdownService.getFileById(fileId);
    if (file == null) return false;

    try {
      final auth = _authService.getCurrentAuth();
      if (auth == null) return false;

      final url = '${auth.serverUrl}api/markdown-sync.php';
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'X-Device-ID': _getDeviceId(),
        },
        body: json.encode({
          'action': 'sync',
          'filename': file.fullPath,
          'content': file.content,
          'timestamp': file.updatedAt,
          'version': file.versionNumber,
          'force': true,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          file.markAsSynced(
            serverVersion: data['version_number'],
            serverTimestamp: data['server_timestamp'],
          );
          return true;
        }
      }
      return false;
    } catch (e) {
      debugPrint('Error force uploading file: $e');
      return false;
    }
  }
}
