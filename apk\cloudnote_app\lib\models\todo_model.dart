import 'package:hive/hive.dart';

part 'todo_model.g.dart';

/// 任务状态枚举
@HiveType(typeId: 5)
enum TodoStatus {
  @HiveField(0)
  pending,    // 未完成
  @HiveField(1)
  completed,  // 已完成
  @HiveField(2)
  priority,   // 优先
  @HiveField(3)
  delayed,    // 延后
  @HiveField(4)
  sleeping,   // 休眠
  @HiveField(5)
  cancelled,  // 已取消
  @HiveField(6)
  archived,   // 已归档
}

/// 任务优先级枚举
@HiveType(typeId: 6)
enum TodoPriority {
  @HiveField(0)
  low,        // 低优先级
  @HiveField(1)
  normal,     // 普通优先级
  @HiveField(2)
  high,       // 高优先级
  @HiveField(3)
  urgent,     // 紧急
}

/// 待办事项数据模型
@HiveType(typeId: 4)
class TodoModel extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String? title;

  @HiveField(2)
  String? description;

  @HiveField(3)
  TodoStatus status;

  @HiveField(4)
  TodoPriority priority;

  @HiveField(5)
  DateTime? dueDate;

  @HiveField(6)
  DateTime? reminderTime;

  @HiveField(7)
  final DateTime createdAt;

  @HiveField(8)
  DateTime updatedAt;

  @HiveField(9)
  String? category;

  @HiveField(10)
  String? audioPath;

  @HiveField(11)
  String? imagePath;

  @HiveField(12)
  List<String>? tags;

  @HiveField(13)
  String? notes;

  @HiveField(14)
  bool isArchived;

  @HiveField(15)
  String? serverId; // 用于与后端同步

  @HiveField(16)
  DateTime? completedAt; // 完成时间

  @HiveField(17)
  bool isDeleted; // 是否已删除（软删除）

  @HiveField(18)
  DateTime? deletedAt; // 删除时间

  @HiveField(19)
  bool needsSync; // 是否需要同步到服务器

  TodoModel({
    required this.id,
    this.title,
    this.description,
    this.status = TodoStatus.pending,
    this.priority = TodoPriority.normal,
    this.dueDate,
    this.reminderTime,
    required this.createdAt,
    required this.updatedAt,
    this.category,
    this.audioPath,
    this.imagePath,
    this.tags,
    this.notes,
    this.isArchived = false,
    this.serverId,
    this.completedAt,
    this.isDeleted = false,
    this.deletedAt,
    this.needsSync = false,
  });

  /// 创建新的待办事项
  factory TodoModel.create({
    String? title,
    String? description,
    TodoStatus status = TodoStatus.pending,
    TodoPriority priority = TodoPriority.normal,
    DateTime? dueDate,
    DateTime? reminderTime,
    String? category,
    String? audioPath,
    String? imagePath,
    List<String>? tags,
    String? notes,
  }) {
    final now = DateTime.now();
    final displayText = title ?? description ?? '新任务';
    return TodoModel(
      id: 'todo_${now.millisecondsSinceEpoch}_${displayText.hashCode}',
      title: title,
      description: description,
      status: status,
      priority: priority,
      dueDate: dueDate,
      reminderTime: reminderTime,
      createdAt: now,
      updatedAt: now,
      category: category,
      audioPath: audioPath,
      imagePath: imagePath,
      tags: tags,
      notes: notes,
      needsSync: true, // 新创建的待办事项需要同步
    );
  }

  /// 更新待办事项
  TodoModel copyWith({
    String? title,
    String? description,
    TodoStatus? status,
    TodoPriority? priority,
    DateTime? dueDate,
    DateTime? reminderTime,
    String? category,
    String? audioPath,
    String? imagePath,
    List<String>? tags,
    String? notes,
    bool? isArchived,
    String? serverId,
    DateTime? completedAt,
    bool? isDeleted,
    DateTime? deletedAt,
    bool? needsSync,
    DateTime? updatedAt,
  }) {
    return TodoModel(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      dueDate: dueDate ?? this.dueDate,
      reminderTime: reminderTime ?? this.reminderTime,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      category: category ?? this.category,
      audioPath: audioPath ?? this.audioPath,
      imagePath: imagePath ?? this.imagePath,
      tags: tags ?? this.tags,
      notes: notes ?? this.notes,
      isArchived: isArchived ?? this.isArchived,
      serverId: serverId ?? this.serverId,
      completedAt: completedAt ?? this.completedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
      needsSync: needsSync ?? (updatedAt != null ? true : this.needsSync),
    );
  }

  /// 转换为JSON格式（用于与后端同步）
  Map<String, dynamic> toJson() {
    return {
      'client_id': id,
      'title': title,
      'description': description,
      'status': status.name,
      'priority': priority.name,
      'due_date': dueDate?.toIso8601String(),
      'reminder_time': reminderTime?.toIso8601String(),
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'completed_at': completedAt?.millisecondsSinceEpoch,
      'category': category,
      'audio_path': audioPath,
      'image_path': imagePath,
      'tags': tags?.join(','),
      'notes': notes,
      'is_archived': isArchived,
      'is_deleted': isDeleted,
      'deleted_at': deletedAt?.millisecondsSinceEpoch,
    };
  }

  /// 从JSON创建TodoModel（用于从后端同步）
  factory TodoModel.fromJson(Map<String, dynamic> json) {
    return TodoModel(
      id: json['client_id'] as String,
      title: json['title'] as String?,
      description: json['description'] as String?,
      status: TodoStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TodoStatus.pending,
      ),
      priority: TodoPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => TodoPriority.normal,
      ),
      dueDate: json['due_date'] != null ? DateTime.parse(json['due_date']) : null,
      reminderTime: json['reminder_time'] != null ? DateTime.parse(json['reminder_time']) : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(json['updated_at'] as int),
      completedAt: json['completed_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['completed_at'] as int)
          : null,
      category: json['category'] as String?,
      audioPath: json['audio_path'] as String?,
      imagePath: json['image_path'] as String?,
      tags: json['tags'] != null && (json['tags'] as String).isNotEmpty
          ? (json['tags'] as String).split(',')
          : null,
      notes: json['notes'] as String?,
      isArchived: json['is_archived'] == 1 || json['is_archived'] == true,
      serverId: json['id']?.toString(),
      isDeleted: json['is_deleted'] == 1 || json['is_deleted'] == true,
      deletedAt: json['deleted_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['deleted_at'] as int)
          : null,
      needsSync: false, // 从服务器同步的数据不需要再次同步
    );
  }



  /// 获取状态显示文本
  String get statusText {
    switch (status) {
      case TodoStatus.pending:
        return '未完成';
      case TodoStatus.completed:
        return '已完成';
      case TodoStatus.priority:
        return '优先';
      case TodoStatus.delayed:
        return '延后';
      case TodoStatus.sleeping:
        return '休眠';
      case TodoStatus.cancelled:
        return '已取消';
      case TodoStatus.archived:
        return '已归档';
    }
  }

  /// 获取优先级显示文本
  String get priorityText {
    switch (priority) {
      case TodoPriority.low:
        return '低';
      case TodoPriority.normal:
        return '普通';
      case TodoPriority.high:
        return '高';
      case TodoPriority.urgent:
        return '紧急';
    }
  }

  /// 获取状态颜色
  int get statusColor {
    switch (status) {
      case TodoStatus.pending:
        return 0xFF2196F3; // 蓝色
      case TodoStatus.completed:
        return 0xFF4CAF50; // 绿色
      case TodoStatus.priority:
        return 0xFFFF5722; // 深橙色
      case TodoStatus.delayed:
        return 0xFFFF9800; // 橙色
      case TodoStatus.sleeping:
        return 0xFF9E9E9E; // 灰色
      case TodoStatus.cancelled:
        return 0xFF757575; // 深灰色
      case TodoStatus.archived:
        return 0xFF795548; // 棕色
    }
  }

  /// 获取优先级颜色
  int get priorityColor {
    switch (priority) {
      case TodoPriority.low:
        return 0xFF4CAF50; // 绿色
      case TodoPriority.normal:
        return 0xFF2196F3; // 蓝色
      case TodoPriority.high:
        return 0xFFFF9800; // 橙色
      case TodoPriority.urgent:
        return 0xFFF44336; // 红色
    }
  }

  /// 是否已过期
  bool get isOverdue {
    if (dueDate == null || status == TodoStatus.completed) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  /// 是否即将到期（24小时内）
  bool get isDueSoon {
    if (dueDate == null || status == TodoStatus.completed) return false;
    final now = DateTime.now();
    final timeDiff = dueDate!.difference(now);
    return timeDiff.inHours <= 24 && timeDiff.inHours > 0;
  }

  /// 获取显示标题（如果没有标题，使用描述的前30个字符）
  String get displayTitle {
    if (title != null && title!.isNotEmpty) {
      return title!;
    }
    if (description != null && description!.isNotEmpty) {
      return description!.length > 30
          ? '${description!.substring(0, 30)}...'
          : description!;
    }
    return '无标题任务';
  }

  /// 获取显示描述（如果标题来自描述，则不显示描述）
  String? get displayDescription {
    if (title != null && title!.isNotEmpty) {
      return description;
    }
    return null; // 标题已经使用了描述内容
  }

  /// 检查是否应该自动归档（完成超过2天）
  bool get shouldAutoArchive {
    if (status != TodoStatus.completed || completedAt == null) {
      return false;
    }
    final now = DateTime.now();
    final daysSinceCompleted = now.difference(completedAt!).inDays;
    return daysSinceCompleted >= 2;
  }
}
