import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/markdown_model.dart';
import '../models/tab_model.dart'; // 导入SyncStatus

/// Markdown文件本地管理服务
class MarkdownService {
  static const String _boxName = 'markdown_files';
  static const String _folderName = 'markdown_files';
  
  Box<MarkdownModel>? _box;
  Directory? _markdownDir;
  bool _isInitialized = false;

  /// 初始化服务
  Future<void> init() async {
    if (_isInitialized) return;

    try {
      // 初始化Hive box
      _box = await Hive.openBox<MarkdownModel>(_boxName);
      
      // 初始化文件目录
      await _initializeDirectory();
      
      // 同步本地文件和数据库
      await _syncLocalFiles();
      
      _isInitialized = true;
      debugPrint('MarkdownService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize MarkdownService: $e');
      rethrow;
    }
  }

  /// 初始化Markdown文件目录
  Future<void> _initializeDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    _markdownDir = Directory(path.join(appDir.path, _folderName));
    
    if (!await _markdownDir!.exists()) {
      await _markdownDir!.create(recursive: true);
      debugPrint('Created markdown directory: ${_markdownDir!.path}');
    }
  }

  /// 同步本地文件和数据库记录
  Future<void> _syncLocalFiles() async {
    if (_markdownDir == null || _box == null) return;

    try {
      // 获取所有本地文件
      final files = await _markdownDir!.list()
          .where((entity) => entity is File && entity.path.endsWith('.md'))
          .cast<File>()
          .toList();

      // 检查数据库中的记录是否对应实际文件
      final dbFiles = _box!.values.where((model) => !model.isDeleted).toList();
      
      for (final model in dbFiles) {
        final file = File(path.join(_markdownDir!.path, model.filename));
        if (!await file.exists()) {
          // 文件不存在，标记为删除
          model.markAsDeleted();
          debugPrint('Marked missing file as deleted: ${model.filename}');
        }
      }

      // 检查本地文件是否在数据库中有记录
      for (final file in files) {
        final filename = path.basename(file.path);
        final existingModel = _box!.values
            .where((model) => model.filename == filename && !model.isDeleted)
            .firstOrNull;

        if (existingModel == null) {
          // 文件存在但数据库中没有记录，创建新记录
          final content = await file.readAsString();
          final model = MarkdownModel.create(
            filename: filename,
            content: content,
          );
          await _box!.put(model.id, model);
          debugPrint('Added orphaned file to database: $filename');
        }
      }
    } catch (e) {
      debugPrint('Error syncing local files: $e');
    }
  }

  /// 获取所有Markdown文件
  List<MarkdownModel> getAllFiles({bool includeDeleted = false}) {
    _ensureInitialized();
    return _box!.values
        .where((model) => includeDeleted || !model.isDeleted)
        .toList()
        ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  /// 根据ID获取文件
  MarkdownModel? getFileById(String id) {
    _ensureInitialized();
    return _box!.get(id);
  }

  /// 根据文件名获取文件
  MarkdownModel? getFileByName(String filename) {
    _ensureInitialized();
    return _box!.values
        .where((model) => model.filename == filename && !model.isDeleted)
        .firstOrNull;
  }

  /// 根据完整路径获取文件
  MarkdownModel? getFileByPath(String fullPath) {
    _ensureInitialized();
    return _box!.values
        .where((model) => model.fullPath == fullPath && !model.isDeleted)
        .firstOrNull;
  }

  /// 创建新的Markdown文件
  Future<MarkdownModel> createFile({
    required String filename,
    String content = '',
  }) async {
    _ensureInitialized();

    // 确保文件名以.md结尾
    final normalizedFilename = filename.endsWith('.md') ? filename : '$filename.md';

    // 检查文件是否已存在（使用完整路径检查）
    final existing = getFileByPath(normalizedFilename);
    if (existing != null) {
      throw Exception('文件已存在: $normalizedFilename');
    }

    // 创建模型
    final model = MarkdownModel.create(
      filename: normalizedFilename,
      content: content,
    );

    // 保存到数据库
    await _box!.put(model.id, model);

    // 保存到文件系统
    await _saveToFile(model);

    debugPrint('Created markdown file: $normalizedFilename');
    return model;
  }

  /// 更新文件内容
  Future<void> updateFileContent(String id, String content) async {
    _ensureInitialized();

    final model = _box!.get(id);
    if (model == null) {
      throw Exception('文件不存在: $id');
    }

    if (model.isDeleted) {
      throw Exception('文件已删除: ${model.filename}');
    }

    // 更新内容
    model.updateContent(content);

    // 保存到文件系统
    await _saveToFile(model);

    debugPrint('Updated markdown file: ${model.filename}');
  }

  /// 重命名文件
  Future<void> renameFile(String id, String newPath) async {
    _ensureInitialized();

    final model = _box!.get(id);
    if (model == null) {
      throw Exception('文件不存在: $id');
    }

    if (model.isDeleted) {
      throw Exception('文件已删除: ${model.fullPath}');
    }

    // 确保新路径以.md结尾
    final normalizedPath = newPath.endsWith('.md') ? newPath : '$newPath.md';

    // 检查新路径是否已存在
    final existing = getFileByPath(normalizedPath);
    if (existing != null && existing.id != id) {
      throw Exception('文件路径已存在: $normalizedPath');
    }

    // 解析新的路径信息
    final pathInfo = MarkdownModel.parseFilePath(normalizedPath);

    // 删除旧文件
    final oldFile = File(path.join(_markdownDir!.path, model.fullPath));
    if (await oldFile.exists()) {
      await oldFile.delete();
    }

    // 更新文件信息
    final oldPath = model.fullPath;
    model.filename = pathInfo['filename']!;
    model.filePath = pathInfo['filePath']!;
    model.fullPath = pathInfo['fullPath']!;
    model.updatedAt = DateTime.now().millisecondsSinceEpoch;
    model.isModified = true;
    model.syncStatus = SyncStatus.needsUpload;
    await model.save();

    // 保存到新文件
    await _saveToFile(model);

    debugPrint('Renamed markdown file: $oldPath -> ${model.fullPath}');
  }

  /// 删除文件
  Future<void> deleteFile(String id) async {
    _ensureInitialized();

    final model = _box!.get(id);
    if (model == null) {
      throw Exception('文件不存在: $id');
    }

    // 标记为删除
    model.markAsDeleted();

    // 删除物理文件
    final file = File(path.join(_markdownDir!.path, model.fullPath));
    if (await file.exists()) {
      await file.delete();
    }

    debugPrint('Deleted markdown file: ${model.fullPath}');
  }

  /// 保存模型到文件系统
  Future<void> _saveToFile(MarkdownModel model) async {
    if (_markdownDir == null) return;

    final filePath = path.join(_markdownDir!.path, model.fullPath);
    final file = File(filePath);

    // 确保目录存在
    final directory = file.parent;
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    await file.writeAsString(model.content);
  }

  /// 获取需要同步的文件
  List<MarkdownModel> getFilesNeedingSync() {
    _ensureInitialized();
    return _box!.values
        .where((model) => model.syncStatus != SyncStatus.synced)
        .toList();
  }

  /// 获取已删除但需要同步的文件
  List<MarkdownModel> getDeletedFilesNeedingSync() {
    _ensureInitialized();
    return _box!.values
        .where((model) => model.isDeleted && model.syncStatus == SyncStatus.needsUpload)
        .toList();
  }

  /// 获取所有已删除的文件
  List<MarkdownModel> getAllDeletedFiles() {
    _ensureInitialized();
    return _box!.values
        .where((model) => model.isDeleted)
        .toList();
  }



  /// 保存模型到数据库（公共方法）
  Future<void> saveModelToDatabase(MarkdownModel model) async {
    _ensureInitialized();
    await _box!.put(model.id, model);
  }

  /// 保存模型到文件系统（公共方法）
  Future<void> saveModelToFile(MarkdownModel model) async {
    await _saveToFile(model);
  }

  /// 删除物理文件
  Future<void> deletePhysicalFile(String fullPath) async {
    if (_markdownDir == null) return;

    final file = File(path.join(_markdownDir!.path, fullPath));
    if (await file.exists()) {
      await file.delete();
      debugPrint('Deleted physical file: $fullPath');
    }
  }

  /// 从数据库中移除文件记录
  Future<void> removeFileFromDatabase(String id) async {
    _ensureInitialized();
    await _box!.delete(id);
    debugPrint('Removed file from database: $id');
  }

  /// 获取文件数量
  int get fileCount {
    _ensureInitialized();
    return _box!.values.where((model) => !model.isDeleted).length;
  }

  /// 确保服务已初始化
  void _ensureInitialized() {
    if (!_isInitialized || _box == null) {
      throw Exception('MarkdownService not initialized');
    }
  }

  /// 清理服务
  Future<void> dispose() async {
    await _box?.close();
    _box = null;
    _markdownDir = null;
    _isInitialized = false;
  }
}
