<?xml version="1.0" encoding="utf-8"?>
<shortcuts xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 快速写入MD文件快捷方式 -->
    <shortcut
        android:shortcutId="quick_write_md"
        android:enabled="true"
        android:icon="@drawable/ic_quick_write_md"
        android:shortcutShortLabel="@string/quick_write_md_short"
        android:shortcutLongLabel="@string/quick_write_md_long"
        android:shortcutDisabledMessage="@string/quick_write_md_disabled">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.cloudnote.app.cloudnote_app"
            android:targetClass="com.cloudnote.app.cloudnote_app.MainActivity">
            <extra android:name="shortcut_action" android:value="quick_write_md" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>

    <!-- 拍照ToMD快捷方式 -->
    <shortcut
        android:shortcutId="photo_to_md"
        android:enabled="true"
        android:icon="@drawable/ic_photo_to_md"
        android:shortcutShortLabel="@string/photo_to_md_short"
        android:shortcutLongLabel="@string/photo_to_md_long"
        android:shortcutDisabledMessage="@string/photo_to_md_disabled">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.cloudnote.app.cloudnote_app"
            android:targetClass="com.cloudnote.app.cloudnote_app.MainActivity">
            <extra android:name="shortcut_action" android:value="photo_to_md" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>



    <!-- 智能创建任务快捷方式 -->
    <shortcut
        android:shortcutId="smart_todo"
        android:enabled="true"
        android:icon="@drawable/ic_smart_todo"
        android:shortcutShortLabel="@string/smart_todo_short"
        android:shortcutLongLabel="@string/smart_todo_long"
        android:shortcutDisabledMessage="@string/smart_todo_disabled">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.cloudnote.app.cloudnote_app"
            android:targetClass="com.cloudnote.app.cloudnote_app.MainActivity">
            <extra android:name="shortcut_action" android:value="smart_todo" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>

    <!-- 新建任务快捷方式 -->
    <shortcut
        android:shortcutId="create_todo"
        android:enabled="true"
        android:icon="@drawable/ic_create_todo"
        android:shortcutShortLabel="@string/create_todo_short"
        android:shortcutLongLabel="@string/create_todo_long"
        android:shortcutDisabledMessage="@string/create_todo_disabled">
        <intent
            android:action="android.intent.action.VIEW"
            android:targetPackage="com.cloudnote.app.cloudnote_app"
            android:targetClass="com.cloudnote.app.cloudnote_app.MainActivity">
            <extra android:name="shortcut_action" android:value="create_todo" />
        </intent>
        <categories android:name="android.shortcut.conversation" />
    </shortcut>
</shortcuts>
