import 'package:hive/hive.dart';
import 'tab_model.dart'; // 导入SyncStatus

part 'markdown_model.g.dart';

@HiveType(typeId: 7)
class MarkdownModel extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String filename;

  @HiveField(2)
  String content;

  @HiveField(3)
  String contentHash;

  @HiveField(4)
  int fileSize;

  @HiveField(5)
  int versionNumber;

  @HiveField(6)
  int createdAt;

  @HiveField(7)
  int updatedAt;

  @HiveField(8)
  int? lastSyncAt;

  @HiveField(9)
  String? deviceId;

  @HiveField(10)
  bool isDeleted;

  @HiveField(11)
  int? deletedAt;

  @HiveField(12)
  SyncStatus syncStatus;

  @HiveField(13)
  bool isModified;

  @HiveField(14)
  String filePath;

  @HiveField(15)
  String fullPath;

  MarkdownModel({
    required this.id,
    required this.filename,
    required this.content,
    required this.contentHash,
    required this.fileSize,
    required this.versionNumber,
    required this.createdAt,
    required this.updatedAt,
    this.lastSyncAt,
    this.deviceId,
    this.isDeleted = false,
    this.deletedAt,
    this.syncStatus = SyncStatus.needsUpload,
    this.isModified = false,
    this.filePath = '',
    this.fullPath = '',
  });

  // 从JSON创建实例
  factory MarkdownModel.fromJson(Map<String, dynamic> json) {
    // 生成客户端ID，如果服务器没有提供
    final serverId = json['id']?.toString() ?? '';
    final clientId = serverId.isNotEmpty ? 'server_$serverId' : 'md_${DateTime.now().millisecondsSinceEpoch}_${json['filename'].hashCode}';

    // 解析路径信息
    final filename = json['filename'] ?? '';
    final filePath = json['file_path'] ?? '';
    final fullPath = json['full_path'] ?? filename;

    return MarkdownModel(
      id: clientId,
      filename: filename,
      content: json['content'] ?? '',
      contentHash: json['content_hash'] ?? '',
      fileSize: (json['file_size'] is int) ? json['file_size'] : int.tryParse(json['file_size']?.toString() ?? '0') ?? 0,
      versionNumber: (json['version_number'] is int) ? json['version_number'] : int.tryParse(json['version_number']?.toString() ?? '1') ?? 1,
      createdAt: (json['created_at'] is int) ? json['created_at'] : int.tryParse(json['created_at']?.toString() ?? '0') ?? 0,
      updatedAt: (json['updated_at'] is int) ? json['updated_at'] : int.tryParse(json['updated_at']?.toString() ?? '0') ?? 0,
      lastSyncAt: json['last_sync_at'] != null ? ((json['last_sync_at'] is int) ? json['last_sync_at'] : int.tryParse(json['last_sync_at'].toString())) : null,
      deviceId: json['device_id'],
      isDeleted: json['is_deleted'] == 1 || json['is_deleted'] == true,
      deletedAt: json['deleted_at'] != null ? ((json['deleted_at'] is int) ? json['deleted_at'] : int.tryParse(json['deleted_at'].toString())) : null,
      syncStatus: SyncStatus.synced,
      isModified: false,
      filePath: filePath,
      fullPath: fullPath,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filename': filename,
      'file_path': filePath,
      'full_path': fullPath,
      'content': content,
      'content_hash': contentHash,
      'file_size': fileSize,
      'version_number': versionNumber,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'last_sync_at': lastSyncAt,
      'device_id': deviceId,
      'is_deleted': isDeleted ? 1 : 0,
      'deleted_at': deletedAt,
    };
  }

  // 创建新的Markdown文件
  factory MarkdownModel.create({
    required String filename,
    String content = '',
    String? deviceId,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;
    final id = 'md_${now}_${filename.hashCode}';

    // 解析路径信息
    final pathInfo = parseFilePath(filename);

    return MarkdownModel(
      id: id,
      filename: pathInfo['filename']!,
      content: content,
      contentHash: _calculateContentHash(content),
      fileSize: content.length,
      versionNumber: 1,
      createdAt: now,
      updatedAt: now,
      deviceId: deviceId,
      syncStatus: SyncStatus.needsUpload,
      isModified: true,
      filePath: pathInfo['filePath']!,
      fullPath: pathInfo['fullPath']!,
    );
  }

  // 解析文件路径信息
  static Map<String, String> parseFilePath(String fullPath) {
    // 标准化路径分隔符
    final normalizedPath = fullPath.replaceAll('\\', '/');

    // 分离路径和文件名
    final lastSlashIndex = normalizedPath.lastIndexOf('/');

    if (lastSlashIndex == -1) {
      // 没有路径，只有文件名
      return {
        'filename': normalizedPath,
        'filePath': '',
        'fullPath': normalizedPath,
      };
    } else {
      // 有路径
      final filePath = normalizedPath.substring(0, lastSlashIndex);
      final filename = normalizedPath.substring(lastSlashIndex + 1);

      return {
        'filename': filename,
        'filePath': filePath,
        'fullPath': normalizedPath,
      };
    }
  }

  // 计算内容哈希
  static String _calculateContentHash(String content) {
    // 简单的哈希计算，实际应用中可以使用crypto库
    return content.hashCode.toString();
  }

  // 更新内容
  void updateContent(String newContent) {
    if (content != newContent) {
      content = newContent;
      contentHash = _calculateContentHash(newContent);
      fileSize = newContent.length;
      updatedAt = DateTime.now().millisecondsSinceEpoch;
      isModified = true;
      syncStatus = SyncStatus.needsUpload;
      save();
    }
  }

  // 标记为已同步
  void markAsSynced({int? serverVersion, int? serverTimestamp}) {
    syncStatus = SyncStatus.synced;
    isModified = false;
    lastSyncAt = DateTime.now().millisecondsSinceEpoch;
    
    if (serverVersion != null) {
      versionNumber = serverVersion;
    }
    
    if (serverTimestamp != null) {
      updatedAt = serverTimestamp;
    }
    
    save();
  }

  // 标记为删除
  void markAsDeleted() {
    isDeleted = true;
    deletedAt = DateTime.now().millisecondsSinceEpoch;
    updatedAt = deletedAt!;
    isModified = true;
    syncStatus = SyncStatus.needsUpload;
    save();
  }

  // 复制实例
  MarkdownModel copyWith({
    String? id,
    String? filename,
    String? content,
    String? contentHash,
    int? fileSize,
    int? versionNumber,
    int? createdAt,
    int? updatedAt,
    int? lastSyncAt,
    String? deviceId,
    bool? isDeleted,
    int? deletedAt,
    SyncStatus? syncStatus,
    bool? isModified,
    String? filePath,
    String? fullPath,
  }) {
    return MarkdownModel(
      id: id ?? this.id,
      filename: filename ?? this.filename,
      content: content ?? this.content,
      contentHash: contentHash ?? this.contentHash,
      fileSize: fileSize ?? this.fileSize,
      versionNumber: versionNumber ?? this.versionNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastSyncAt: lastSyncAt ?? this.lastSyncAt,
      deviceId: deviceId ?? this.deviceId,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
      syncStatus: syncStatus ?? this.syncStatus,
      isModified: isModified ?? this.isModified,
      filePath: filePath ?? this.filePath,
      fullPath: fullPath ?? this.fullPath,
    );
  }

  // 获取显示名称（不包含.md后缀）
  String get displayName {
    return filename.replaceAll('.md', '');
  }

  // 获取目录名称
  String get directoryName {
    if (filePath.isEmpty) return '';
    return filePath.split('/').last;
  }

  // 是否在根目录
  bool get isInRootDirectory {
    return filePath.isEmpty;
  }

  // 获取目录层级
  int get directoryLevel {
    if (filePath.isEmpty) return 0;
    return filePath.split('/').length;
  }

  @override
  String toString() {
    return 'MarkdownModel(id: $id, fullPath: $fullPath, size: $fileSize, version: $versionNumber, modified: $isModified)';
  }
}


